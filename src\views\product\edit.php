<h1>Modifier le produit</h1>

<form method="post" action="/product/edit/<?= $data['id'] ?>">
    <p>
        <label for="name">Nom :</label>
        <input type="text" id="name" name="name" value="<?= htmlspecialchars($data['name']) ?>" required>
    </p>
    <p>
        <label for="description">Description :</label>
        <textarea id="description" name="description" required><?= htmlspecialchars($data['description']) ?></textarea>
    </p>
    <p>
        <label for="category">Catégorie :</label>
        <select id="category" name="category" required>
            <option value="">Sélectionner une catégorie</option>
            <?php foreach($categories as $category): ?>
                <option value="<?= $category['id'] ?>" <?= $data['category_id'] == $category['id'] ? 'selected' : '' ?>>
                    <?= htmlspecialchars($category['name']) ?>
                </option>
            <?php endforeach; ?>
        </select>
    </p>
    <p>
        <label for="pvc">Prix de vente conseillé (en €) :</label>
        <input type="number" id="pvc" name="pvc" step="0.01" value="<?= htmlspecialchars($data['pvc']) ?>" required>
    </p>
    <p>
        <label for="weight">Poids (en kg) :</label>
        <input type="number" id="weight" name="weight" step="0.01" value="<?= htmlspecialchars($data['weight']) ?>" required>
    </p>
    <p>
        <label for="manufacturer">Fabricant :</label>
        <select id="manufacturer" name="manufacturer" required>
            <option value="">Sélectionner un fabricant</option>
            <?php foreach($manufacturers as $manufacturer): ?>
                <option value="<?= $manufacturer['id'] ?>" <?= $data['manufacturer_id'] == $manufacturer['id'] ? 'selected' : '' ?>>
                    <?= htmlspecialchars($manufacturer['name']) ?>
                </option>
            <?php endforeach; ?>
        </select>
    </p>
    <p>
        <button type="submit">Modifier</button>
        <a href="/product/index">Annuler</a>
    </p>
</form>
