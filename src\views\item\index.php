<h1>Articles en stock</h1>

<nav>
    <a href="/item/add">Ajouter un article</a>
</nav>

<table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <caption>Liste des articles</caption>
    <thead>
        <tr>
            <th scope="col">ID</th>
            <th scope="col">Produit</th>
            <th scope="col">Quantité</th>
            <th scope="col">Entrepôt</th>
            <th scope="col">Emplacement</th>
            <th scope="col">Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($data as $value): ?>
            <tr>
                <td><?= htmlspecialchars($value["id"]) ?></td>
                <td><?= htmlspecialchars($value["product_name"]) ?></td>
                <td><?= htmlspecialchars($value["quantity"]) ?></td>
                <td><?= htmlspecialchars($value["warehouse_name"]) ?></td>
                <td>
                    Allée <?= htmlspecialchars($value["warehouse_aisle"]) ?> -
                    Étagère <?= htmlspecialchars($value["warehouse_shelf"]) ?> -
                    Niveau <?= htmlspecialchars($value["warehouse_level"]) ?> -
                    Position <?= htmlspecialchars($value["warehouse_position"]) ?>
                </td>
                <td>
                    <details>
                        <summary>Actions</summary>
                        <ul>
                            <li><a href="view/<?= $value["id"] ?>">Détails</a></li>
                            <li><a href="edit/<?= $value["id"] ?>">Modifier</a></li>
                            <li><a href="delete/<?= $value["id"] ?>">Supprimer</a></li>
                        </ul>
                    </details>
                </td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>
