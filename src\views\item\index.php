<h1>Articles en stock</h1>

<nav>
    <a href="/item/add">Ajouter un article</a>
</nav>

<!-- Formulaire de recherche et filtres -->
<form method="get" action="/item/index" style="margin: 20px 0; padding: 15px; border: 1px solid #ccc; background-color: #f9f9f9;">
    <fieldset>
        <legend>Recherche et filtres</legend>

        <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: end;">
            <div>
                <label for="search">Rechercher :</label><br>
                <input type="text" id="search" name="search" placeholder="Nom du produit..."
                       value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" style="width: 200px;">
            </div>

            <div>
                <label for="warehouse_id">Entrepôt :</label><br>
                <select id="warehouse_id" name="warehouse_id" style="width: 150px;">
                    <option value="">Tous les entrepôts</option>
                    <?php foreach($warehouses as $warehouse): ?>
                        <option value="<?= $warehouse['id'] ?>"
                                <?= ($_GET['warehouse_id'] ?? '') == $warehouse['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($warehouse['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div>
                <label for="category_id">Catégorie :</label><br>
                <select id="category_id" name="category_id" style="width: 150px;">
                    <option value="">Toutes les catégories</option>
                    <?php foreach($categories as $category): ?>
                        <option value="<?= $category['id'] ?>"
                                <?= ($_GET['category_id'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div>
                <label for="sort">Trier par :</label><br>
                <select id="sort" name="sort" style="width: 150px;">
                    <option value="">Par défaut</option>
                    <option value="product_name" <?= ($_GET['sort'] ?? '') == 'product_name' ? 'selected' : '' ?>>Nom du produit</option>
                    <option value="warehouse_name" <?= ($_GET['sort'] ?? '') == 'warehouse_name' ? 'selected' : '' ?>>Entrepôt</option>
                    <option value="category_name" <?= ($_GET['sort'] ?? '') == 'category_name' ? 'selected' : '' ?>>Catégorie</option>
                    <option value="quantity" <?= ($_GET['sort'] ?? '') == 'quantity' ? 'selected' : '' ?>>Quantité</option>
                </select>
            </div>

            <div>
                <button type="submit" style="padding: 8px 15px;">Filtrer</button>
                <a href="/item/index" style="padding: 8px 15px; text-decoration: none; background-color: #ddd; color: black; margin-left: 5px;">Réinitialiser</a>
            </div>
        </div>
    </fieldset>
</form>

<table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <caption>Liste des articles</caption>
    <thead>
        <tr>
            <th scope="col">ID</th>
            <th scope="col">Produit</th>
            <th scope="col">Catégorie</th>
            <th scope="col">Quantité</th>
            <th scope="col">Entrepôt</th>
            <th scope="col">Emplacement</th>
            <th scope="col">Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($data as $value): ?>
            <tr>
                <td><?= htmlspecialchars($value["id"]) ?></td>
                <td><?= htmlspecialchars($value["product_name"]) ?></td>
                <td><?= htmlspecialchars($value["category_name"] ?? 'N/A') ?></td>
                <td><?= htmlspecialchars($value["quantity"]) ?></td>
                <td><?= htmlspecialchars($value["warehouse_name"]) ?></td>
                <td>
                    Allée <?= htmlspecialchars($value["warehouse_aisle"]) ?> -
                    Étagère <?= htmlspecialchars($value["warehouse_shelf"]) ?> -
                    Niveau <?= htmlspecialchars($value["warehouse_level"]) ?> -
                    Position <?= htmlspecialchars($value["warehouse_position"]) ?>
                </td>
                <td>
                    <details>
                        <summary>Actions</summary>
                        <ul>
                            <li><a href="view/<?= $value["id"] ?>">Détails</a></li>
                            <li><a href="edit/<?= $value["id"] ?>">Modifier</a></li>
                            <li><a href="delete/<?= $value["id"] ?>">Supprimer</a></li>
                        </ul>
                    </details>
                </td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>
