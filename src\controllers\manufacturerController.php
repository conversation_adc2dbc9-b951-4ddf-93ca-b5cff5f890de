<?php

class manufacturerController extends Controller {

    public function index() {
        $data = $this->manufacturer->getManufacturers();
        $this->set(compact("data"));
        $this->render();
    }

    public function view($id) {
        $data = $this->manufacturer->getManufacturer($id);
        $this->set(compact("data"));
        $this->render();
    }

    public function add() {
        if(!empty($_POST)) {
            if($this->manufacturer->addManufacturer($_POST['name'], $_POST['description'])) {
                $this->redirect('manufacturer/index');
            }
        }
        $this->render();
    }

    public function edit($id) {
        if(!empty($_POST)) {
            if($this->manufacturer->editManufacturer($id, $_POST['name'], $_POST['description'])) {
                $this->redirect('manufacturer/index');
            }
        }
        $data = $this->manufacturer->getManufacturer($id);
        $this->set(compact("data"));
        $this->render();
    }

    public function delete($id) {
        if($this->manufacturer->removeManufacturer($id)) {
            $this->redirect('manufacturer/index');
        }
    }
}