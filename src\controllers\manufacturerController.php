<?php

class warehouseController extends Controller{

    public function index(){
        $data = $this->warehouse->findAll();
        $this->set(compact("data"));
        $this->render();
    }

    public function view($id, $name){
        $this->set(["data" => $this->warehouse
        ->find([
            "id" => $id,
            "nom" => $name
            ])
        ]);
        $this->render();
    }

}