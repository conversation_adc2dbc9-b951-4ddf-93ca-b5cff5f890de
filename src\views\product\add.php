<h1>Ajouter un produit</h1>

<form method="post" action="/product/add">
    <p>
        <label for="name">Nom :</label>
        <input type="text" id="name" name="name" required>
    </p>
    <p>
        <label for="description">Description :</label>
        <textarea id="description" name="description" required></textarea>
    </p>
    <p>
        <label for="category">Catégorie :</label>
        <select id="category" name="category" required>
            <option value="">Sélectionner une catégorie</option>
            <?php foreach($categories as $category): ?>
                <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
            <?php endforeach; ?>
        </select>
    </p>
    <p>
        <label for="pvc">Prix de vente conseillé (en €) :</label>
        <input type="number" id="pvc" name="pvc" step="0.01" required>
    </p>
    <p>
        <label for="weight">Poids (en kg) :</label>
        <input type="number" id="weight" name="weight" step="0.01" required>
    </p>
    <p>
        <label for="manufacturer">Fabricant :</label>
        <select id="manufacturer" name="manufacturer" required>
            <option value="">Sélectionner un fabricant</option>
            <?php foreach($manufacturers as $manufacturer): ?>
                <option value="<?= $manufacturer['id'] ?>"><?= htmlspecialchars($manufacturer['name']) ?></option>
            <?php endforeach; ?>
        </select>
    </p>
    <p>
        <button type="submit">Ajouter</button>
        <a href="/product/index">Annuler</a>
    </p>
</form>
