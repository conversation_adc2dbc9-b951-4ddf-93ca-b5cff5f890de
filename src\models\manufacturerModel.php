<?php
error_reporting(E_ALL & ~E_DEPRECATED);

class manufacturerModel extends Model {

    public function __init(){
        $this->table = "manufacturer";
    }

    public function getManufacturers(){
        return $this->findAll();
    }

    public function getManufacturer($id){
        return $this->find($id);
    }

    public function getManufacturerByParams($params){
        return $this->find($params);
    }

    public function addManufacturer($name, $description){
        return $this->insert([
            'name' => $name,
            'description' => $description
        ]);
    }

    public function editManufacturer($id, $name, $description){
        return $this->update($id, [
            'name' => $name,
            'description' => $description
        ]);
    }

    public function removeManufacturer($id){
        return $this->delete($id);
    }
}