<?php
error_reporting(E_ALL & ~E_DEPRECATED);

class itemModel extends Model {

    public function __init(){
        $this->table = "item";
    }

    public function getItems(){
        $query = "SELECT i.*, p.name as product_name, w.name as warehouse_name 
                 FROM " . $this->table . " i 
                 LEFT JOIN product p ON i.product_id = p.id
                 LEFT JOIN warehouse w ON i.warehouse_id = w.id";
        $stmt = $this->co->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getItem($id){
        return $this->find($id);
    }

    public function getItemByParams($params){
        return $this->find($params);
    }

    public function addItem($product_id, $quantity, $warehouse_id, $warehouse_aisle, 
                          $warehouse_shelf, $warehouse_level, $warehouse_position){
        return $this->insert([
            'product_id' => $product_id,
            'quantity' => $quantity,
            'warehouse_id' => $warehouse_id,
            'warehouse_aisle' => $warehouse_aisle,
            'warehouse_shelf' => $warehouse_shelf,
            'warehouse_level' => $warehouse_level,
            'warehouse_position' => $warehouse_position
        ]);
    }

    public function editItem($id, $product_id, $quantity, $warehouse_id, $warehouse_aisle, 
                           $warehouse_shelf, $warehouse_level, $warehouse_position){
        return $this->update($id, [
            'product_id' => $product_id,
            'quantity' => $quantity,
            'warehouse_id' => $warehouse_id,
            'warehouse_aisle' => $warehouse_aisle,
            'warehouse_shelf' => $warehouse_shelf,
            'warehouse_level' => $warehouse_level,
            'warehouse_position' => $warehouse_position
        ]);
    }

    public function removeItem($id){
        return $this->delete($id);
    }
}