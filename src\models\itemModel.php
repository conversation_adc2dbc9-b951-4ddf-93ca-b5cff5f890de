<?php
error_reporting(E_ALL & ~E_DEPRECATED);

class itemModel extends Model {

    public function __init(){
        $this->table = "item";
    }

    public function getItems($filters = []){
        $query = "SELECT i.*, p.name as product_name, w.name as warehouse_name, c.name as category_name
                 FROM " . $this->table . " i
                 LEFT JOIN product p ON i.product_id = p.id
                 LEFT JOIN warehouse w ON i.warehouse_id = w.id
                 LEFT JOIN category c ON p.category_id = c.id";

        $conditions = [];
        $params = [];

        // Filtre par entrepôt
        if (!empty($filters['warehouse_id'])) {
            $conditions[] = "i.warehouse_id = :warehouse_id";
            $params['warehouse_id'] = $filters['warehouse_id'];
        }

        // Filtre par catégorie
        if (!empty($filters['category_id'])) {
            $conditions[] = "p.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }

        // Recherche par nom de produit
        if (!empty($filters['search'])) {
            $conditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        // Tri
        $orderBy = "i.id DESC"; // Par défaut
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'product_name':
                    $orderBy = "p.name ASC";
                    break;
                case 'warehouse_name':
                    $orderBy = "w.name ASC";
                    break;
                case 'category_name':
                    $orderBy = "c.name ASC";
                    break;
                case 'quantity':
                    $orderBy = "i.quantity DESC";
                    break;
            }
        }

        $query .= " ORDER BY " . $orderBy;

        $stmt = $this->co->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getItem($id){
        $query = "SELECT i.*, p.name as product_name, w.name as warehouse_name
                 FROM " . $this->table . " i
                 LEFT JOIN product p ON i.product_id = p.id
                 LEFT JOIN warehouse w ON i.warehouse_id = w.id
                 WHERE i.id = :id";
        $stmt = $this->co->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getItemByParams($params){
        return $this->find($params);
    }

    public function addItem($product_id, $quantity, $warehouse_id, $warehouse_aisle, 
                          $warehouse_shelf, $warehouse_level, $warehouse_position){
        return $this->insert([
            'product_id' => $product_id,
            'quantity' => $quantity,
            'warehouse_id' => $warehouse_id,
            'warehouse_aisle' => $warehouse_aisle,
            'warehouse_shelf' => $warehouse_shelf,
            'warehouse_level' => $warehouse_level,
            'warehouse_position' => $warehouse_position
        ]);
    }

    public function editItem($id, $product_id, $quantity, $warehouse_id, $warehouse_aisle, 
                           $warehouse_shelf, $warehouse_level, $warehouse_position){
        return $this->update($id, [
            'product_id' => $product_id,
            'quantity' => $quantity,
            'warehouse_id' => $warehouse_id,
            'warehouse_aisle' => $warehouse_aisle,
            'warehouse_shelf' => $warehouse_shelf,
            'warehouse_level' => $warehouse_level,
            'warehouse_position' => $warehouse_position
        ]);
    }

    public function removeItem($id){
        return $this->delete($id);
    }
}