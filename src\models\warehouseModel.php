<?php
error_reporting(E_ALL & ~E_DEPRECATED);

class warehouseModel extends Model {

    public function __init(){
        $this->table = "warehouse";
    }

    public function getWarehouses(){
        return $this->findAll();
    }

    public function getWarehouse($id){
        return $this->find($id);
    }

    public function getWarehouseByParams($params){
        return $this->find($params);
    }

    public function addWarehouse($name, $slug, $location){
        return $this->insert([
            'name' => $name,
            'slug' => $slug,
            'location' => $location
        ]);
    }

    public function editWarehouse($id, $name, $slug, $location){
        return $this->update($id, [
            'name' => $name,
            'slug' => $slug,
            'location' => $location
        ]);
    }

    public function removeWarehouse($id){
        return $this->delete($id);
    }
}