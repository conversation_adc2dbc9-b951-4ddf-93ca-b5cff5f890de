<?php

class DB{
    private $DB_HOST;
    private $DB_USER = "root";
    private $DB_PASS;
    private $DB_PORT;
    private $DB_NAME;
    protected $co;

    public function __construct(){
        $this->getConfig("default");
        $this->connect();
    }

    private function connect(){
        $dsn = 'mysql:host=' . $this->DB_HOST . ';port=' . $this->DB_PORT . ';dbname=' . $this->DB_NAME . ';charset=utf8';
        try {
            $this->co = new PDO($dsn, $this->DB_USER, $this->DB_PASS);
            $this->co->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            die('Connection failed: ' . $e->getMessage());
        }
    }

    private function getConfig($connectionName){
        $config = require(ROOT.DS.'config.php');
        $this->DB_HOST = $config['connections'][$connectionName]['host'];
        $this->DB_USER = $config['connections'][$connectionName]['user'];
        $this->DB_PASS = $config['connections'][$connectionName]['password'];
        $this->DB_PORT = $config['connections'][$connectionName]['port'];
        $this->DB_NAME = $config['connections'][$connectionName]['dbname'];
    }
}