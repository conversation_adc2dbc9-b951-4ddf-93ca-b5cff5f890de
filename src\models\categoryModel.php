<?php
error_reporting(E_ALL & ~E_DEPRECATED);

class categoryModel extends Model {

    public function __init(){
        $this->table = "category";
    }

    public function getCategories(){
        return $this->findAll();
    }

    public function getCategory($id){
        return $this->find($id);
    }

    public function getCategoryByParams($params){
        return $this->find($params);
    }

    public function addCategory($name, $description){
        return $this->insert([
            'name' => $name,
            'description' => $description
        ]);
    }

    public function editCategory($id, $name, $description){
        return $this->update($id, [
            'name' => $name,
            'description' => $description
        ]);
    }

    public function removeCategory($id){
        return $this->delete($id);
    }
}