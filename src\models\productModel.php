<?php
error_reporting(E_ALL & ~E_DEPRECATED);

class productModel extends Model {

    public function __init(){
        $this->table = "product";
    }

    public function getProducts($filters = []){
        $query = "SELECT p.*, c.name as category_name, m.name as manufacturer_name
                 FROM " . $this->table . " p
                 LEFT JOIN category c ON p.category_id = c.id
                 LEFT JOIN manufacturer m ON p.manufacturer_id = m.id";

        $conditions = [];
        $params = [];

        // Filtre par catégorie
        if (!empty($filters['category_id'])) {
            $conditions[] = "p.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }

        // Filtre par fabricant
        if (!empty($filters['manufacturer_id'])) {
            $conditions[] = "p.manufacturer_id = :manufacturer_id";
            $params['manufacturer_id'] = $filters['manufacturer_id'];
        }

        // Recherche par nom ou description
        if (!empty($filters['search'])) {
            $conditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        // Tri
        $orderBy = "p.id DESC"; // Par défaut
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'name':
                    $orderBy = "p.name ASC";
                    break;
                case 'category_name':
                    $orderBy = "c.name ASC";
                    break;
                case 'manufacturer_name':
                    $orderBy = "m.name ASC";
                    break;
                case 'pvc':
                    $orderBy = "p.pvc DESC";
                    break;
            }
        }

        $query .= " ORDER BY " . $orderBy;

        $stmt = $this->co->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getProduct($id){
        return $this->find($id);
    }

    public function getProductByParams($params){
        return $this->find($params);
    }

    public function addProduct($name, $description, $category, $pvc, $weight, $manufacturer){
        return $this->insert([
            'name' => $name,
            'description' => $description,
            'category_id' => $category,
            'pvc' => $pvc,
            'weight' => $weight,
            'manufacturer_id' => $manufacturer
        ]);
    }

    public function editProduct($id, $name, $description, $category, $pvc, $weight, $manufacturer){
        return $this->update($id, [
            'name' => $name,
            'description' => $description,
            'category_id' => $category,
            'pvc' => $pvc,
            'weight' => $weight,
            'manufacturer_id' => $manufacturer
        ]);
    }

    public function removeProduct($id){
        return $this->delete($id);
    }

}