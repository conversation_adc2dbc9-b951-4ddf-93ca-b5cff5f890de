<?php
error_reporting(E_ALL & ~E_DEPRECATED);

class productModel extends Model {

    public function __init(){
        $this->table = "product";
    }

    public function getProducts(){
        $query = "SELECT p.*, c.name as category_name, m.name as manufacturer_name 
                 FROM " . $this->table . " p 
                 LEFT JOIN category c ON p.category_id = c.id
                 LEFT JOIN manufacturer m ON p.manufacturer_id = m.id";
        $stmt = $this->co->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getProduct($id){
        return $this->find($id);
    }

    public function getProductByParams($params){
        return $this->find($params);
    }

    public function addProduct($name, $description, $category, $pvc, $weight, $manufacturer){
        return $this->insert([
            'name' => $name,
            'description' => $description,
            'category_id' => $category,
            'pvc' => $pvc,
            'weight' => $weight,
            'manufacturer_id' => $manufacturer
        ]);
    }

    public function editProduct($id, $name, $description, $category, $pvc, $weight, $manufacturer){
        return $this->update($id, [
            'name' => $name,
            'description' => $description,
            'category_id' => $category,
            'pvc' => $pvc,
            'weight' => $weight,
            'manufacturer_id' => $manufacturer
        ]);
    }

    public function removeProduct($id){
        return $this->delete($id);
    }

}