<h1>Produits</h1>

<nav>
    <a href="/product/add">Ajouter un produit</a>
</nav>

<!-- Formulaire de recherche et filtres -->
<form method="get" action="/product/index" style="margin: 20px 0; padding: 15px; border: 1px solid #ccc; background-color: #f9f9f9;">
    <fieldset>
        <legend>Recherche et filtres</legend>

        <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: end;">
            <div>
                <label for="search">Rechercher :</label><br>
                <input type="text" id="search" name="search" placeholder="Nom ou description..."
                       value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" style="width: 200px;">
            </div>

            <div>
                <label for="category_id">Catégorie :</label><br>
                <select id="category_id" name="category_id" style="width: 150px;">
                    <option value="">Toutes les catégories</option>
                    <?php foreach($categories as $category): ?>
                        <option value="<?= $category['id'] ?>"
                                <?= ($_GET['category_id'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div>
                <label for="manufacturer_id">Fabricant :</label><br>
                <select id="manufacturer_id" name="manufacturer_id" style="width: 150px;">
                    <option value="">Tous les fabricants</option>
                    <?php foreach($manufacturers as $manufacturer): ?>
                        <option value="<?= $manufacturer['id'] ?>"
                                <?= ($_GET['manufacturer_id'] ?? '') == $manufacturer['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($manufacturer['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div>
                <label for="sort">Trier par :</label><br>
                <select id="sort" name="sort" style="width: 150px;">
                    <option value="">Par défaut</option>
                    <option value="name" <?= ($_GET['sort'] ?? '') == 'name' ? 'selected' : '' ?>>Nom</option>
                    <option value="category_name" <?= ($_GET['sort'] ?? '') == 'category_name' ? 'selected' : '' ?>>Catégorie</option>
                    <option value="manufacturer_name" <?= ($_GET['sort'] ?? '') == 'manufacturer_name' ? 'selected' : '' ?>>Fabricant</option>
                    <option value="pvc" <?= ($_GET['sort'] ?? '') == 'pvc' ? 'selected' : '' ?>>Prix</option>
                </select>
            </div>

            <div>
                <button type="submit" style="padding: 8px 15px;">Filtrer</button>
                <a href="/product/index" style="padding: 8px 15px; text-decoration: none; background-color: #ddd; color: black; margin-left: 5px;">Réinitialiser</a>
            </div>
        </div>
    </fieldset>
</form>

<table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <caption>Liste des produits</caption>
    <thead>
        <tr>
            <th scope="col">ID</th>
            <th scope="col">Nom</th>
            <th scope="col">Catégorie</th>
            <th scope="col">Prix de vente</th>
            <th scope="col">Fabricant</th>
            <th scope="col">Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($data as $value): ?>
            <tr>
                <td><?= htmlspecialchars($value["id"]) ?></td>
                <td><?= htmlspecialchars($value["name"]) ?></td>
                <td><?= htmlspecialchars($value["category_name"]) ?></td>
                <td><?= htmlspecialchars($value["pvc"]) ?> €</td>
                <td><?= htmlspecialchars($value["manufacturer_name"]) ?></td>
                <td>
                    <details>
                        <summary>Actions</summary>
                        <ul>
                            <li><a href="view/<?= $value["id"] ?>">Détails</a></li>
                            <li><a href="edit/<?= $value["id"] ?>">Modifier</a></li>
                            <li><a href="delete/<?= $value["id"] ?>">Supprimer</a></li>
                        </ul>
                    </details>
                </td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>
