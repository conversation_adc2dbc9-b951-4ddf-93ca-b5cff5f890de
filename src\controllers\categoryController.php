<?php

class categoryController extends Controller {
    
    public function index() {
        $data = $this->category->getCategories();
        $this->set(compact("data"));
        $this->render();
    }

    public function view($id) {
        $data = $this->category->getCategory($id);
        $this->set(compact("data"));
        $this->render();
    }

    public function add() {
        if(!empty($_POST)) {
            if($this->category->addCategory($_POST['name'], $_POST['description'])) {
                $this->redirect('category/index');
            }
        }
        $this->render();
    }

    public function edit($id) {
        if(!empty($_POST)) {
            if($this->category->editCategory($id, $_POST['name'], $_POST['description'])) {
                $this->redirect('category/index');
            }
        }
        $data = $this->category->getCategory($id);
        $this->set(compact("data"));
        $this->render();
    }

    public function delete($id) {
        if($this->category->removeCategory($id)) {
            $this->redirect('category/index');
        }
    }
}