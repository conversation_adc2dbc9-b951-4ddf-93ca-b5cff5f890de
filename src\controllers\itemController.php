<?php

class itemController extends Controller {
    
    public function __init() {
        $this->loadModel('product');
        $this->loadModel('warehouse');
    }

    public function index() {
        $data = $this->item->getItems();
        $this->set(compact("data"));
        $this->render();
    }

    public function view($id) {
        $data = $this->item->getItem($id);
        $this->set(compact("data"));
        $this->render();
    }

    public function add() {
        $products = $this->product->getProducts();
        $warehouses = $this->warehouse->getWarehouses();
        
        $this->set(compact("products", "warehouses"));
        if(!empty($_POST)) {
            if($this->item->addItem(
                $_POST['product_id'], 
                $_POST['quantity'],
                $_POST['warehouse_id'],
                $_POST['warehouse_aisle'],
                $_POST['warehouse_shelf'],
                $_POST['warehouse_level'],
                $_POST['warehouse_position']
            )) {
                $this->redirect('item/index');
            }
        }
        $this->render();
    }

    public function edit($id) {
        $products = $this->product->getProducts();
        $warehouses = $this->warehouse->getWarehouses();

        if(!empty($_POST)) {
            if($this->item->editItem(
                $id,
                $_POST['product_id'], 
                $_POST['quantity'],
                $_POST['warehouse_id'],
                $_POST['warehouse_aisle'],
                $_POST['warehouse_shelf'],
                $_POST['warehouse_level'],
                $_POST['warehouse_position']
            )) {
                $this->redirect('item/index');
            }
        }
        $data = $this->item->getItem($id);
        $this->set(compact("data", "products", "warehouses"));
        $this->render();
    }

    public function delete($id) {
        if($this->item->removeItem($id)) {
            $this->redirect('item/index');
        }
    }
}
