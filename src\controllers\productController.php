<?php

class productController extends Controller {
    
    public function __init() {
        $this->loadModel('category');
        $this->loadModel('manufacturer');
    }

    public function index() {
        $data = $this->product->getProducts();
        $this->set(compact("data"));
        $this->render();
    }

    public function view($id) {
        $data = $this->product->getProduct($id);
        $this->set(compact("data"));
        $this->render();
    }

    public function add() {
        $categories = $this->category->getCategories();
        $manufacturers = $this->manufacturer->getManufacturers();
        
        $this->set(compact("categories", "manufacturers"));
        if(!empty($_POST)) {
            if($this->product->addProduct($_POST['name'], $_POST['description'], 
                $_POST['category'], $_POST['pvc'], $_POST['weight'], $_POST['manufacturer'])) {
                $this->redirect('product/index');
            }
        }
        $this->render();
    }

    public function edit($id) {
        $categories = $this->category->getCategories();
        $manufacturers = $this->manufacturer->getManufacturers();

        if(!empty($_POST)) {
            if($this->product->editProduct($id, $_POST['name'], $_POST['description'], 
                $_POST['category'], $_POST['pvc'], $_POST['weight'], $_POST['manufacturer'])) {
                $this->redirect('product/index');
            }
        }
        $data = $this->product->getProduct($id);
        $this->set(compact("data", "categories", "manufacturers"));
        $this->render();
    }

    public function delete($id) {
        if($this->product->removeProduct($id)) {
            $this->redirect('product/index');
        }
    }
}
