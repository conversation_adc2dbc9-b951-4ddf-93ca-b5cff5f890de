<?php

class productController extends Controller {
    
    public function __init() {
        $this->loadModel('category');
        $this->loadModel('manufacturer');
    }

    public function index() {
        // Récupération des filtres depuis GET
        $filters = [];
        if (!empty($_GET['category_id'])) {
            $filters['category_id'] = $_GET['category_id'];
        }
        if (!empty($_GET['manufacturer_id'])) {
            $filters['manufacturer_id'] = $_GET['manufacturer_id'];
        }
        if (!empty($_GET['search'])) {
            $filters['search'] = $_GET['search'];
        }
        if (!empty($_GET['sort'])) {
            $filters['sort'] = $_GET['sort'];
        }

        $data = $this->product->getProducts($filters);
        $categories = $this->category->getCategories();
        $manufacturers = $this->manufacturer->getManufacturers();

        $this->set(compact("data", "categories", "manufacturers"));
        $this->render();
    }

    public function view($id) {
        $data = $this->product->getProduct($id);
        $this->set(compact("data"));
        $this->render();
    }

    public function add() {
        $categories = $this->category->getCategories();
        $manufacturers = $this->manufacturer->getManufacturers();
        
        $this->set(compact("categories", "manufacturers"));
        if(!empty($_POST)) {
            if($this->product->addProduct($_POST['name'], $_POST['description'], 
                $_POST['category'], $_POST['pvc'], $_POST['weight'], $_POST['manufacturer'])) {
                $this->redirect('product/index');
            }
        }
        $this->render();
    }

    public function edit($id) {
        $categories = $this->category->getCategories();
        $manufacturers = $this->manufacturer->getManufacturers();

        if(!empty($_POST)) {
            if($this->product->editProduct($id, $_POST['name'], $_POST['description'], 
                $_POST['category'], $_POST['pvc'], $_POST['weight'], $_POST['manufacturer'])) {
                $this->redirect('product/index');
            }
        }
        $data = $this->product->getProduct($id);
        $this->set(compact("data", "categories", "manufacturers"));
        $this->render();
    }

    public function delete($id) {
        if($this->product->removeProduct($id)) {
            $this->redirect('product/index');
        }
    }
}
