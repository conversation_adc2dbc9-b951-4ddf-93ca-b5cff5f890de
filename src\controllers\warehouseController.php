<?php

class warehouseController extends Controller {

    public function index() {
        $data = $this->warehouse->getWarehouses();
        $this->set(compact("data"));
        $this->render();
    }

    public function view($id) {
        $data = $this->warehouse->getWarehouse($id);
        $this->set(compact("data"));
        $this->render();
    }

    public function add() {
        if(!empty($_POST)) {
            if($this->warehouse->addWarehouse($_POST['name'], $_POST['slug'], $_POST['location'])) {
                $this->redirect('warehouse/index');
            }
        }
        $this->render();
    }

    public function edit($id) {
        if(!empty($_POST)) {
            if($this->warehouse->editWarehouse($id, $_POST['name'], $_POST['slug'], $_POST['location'])) {
                $this->redirect('warehouse/index');
            }
        }
        $data = $this->warehouse->getWarehouse($id);
        $this->set(compact("data"));
        $this->render();
    }

    public function delete($id) {
        if($this->warehouse->removeWarehouse($id)) {
            $this->redirect('warehouse/index');
        }
    }
}