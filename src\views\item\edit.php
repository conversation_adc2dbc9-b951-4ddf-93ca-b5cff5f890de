<h1>Modifier l'article</h1>

<form method="post" action="/item/edit/<?= $data['id'] ?>">
    <p>
        <label for="product_id">Produit :</label>
        <select id="product_id" name="product_id" required>
            <option value="">Sélectionner un produit</option>
            <?php foreach($products as $product): ?>
                <option value="<?= $product['id'] ?>" <?= $data['product_id'] == $product['id'] ? 'selected' : '' ?>>
                    <?= htmlspecialchars($product['name']) ?>
                </option>
            <?php endforeach; ?>
        </select>
    </p>
    <p>
        <label for="quantity">Quantité :</label>
        <input type="number" id="quantity" name="quantity" min="0" value="<?= htmlspecialchars($data['quantity']) ?>" required>
    </p>
    <p>
        <label for="warehouse_id">Entrepôt :</label>
        <select id="warehouse_id" name="warehouse_id" required>
            <option value="">Sélectionner un entrepôt</option>
            <?php foreach($warehouses as $warehouse): ?>
                <option value="<?= $warehouse['id'] ?>" <?= $data['warehouse_id'] == $warehouse['id'] ? 'selected' : '' ?>>
                    <?= htmlspecialchars($warehouse['name']) ?>
                </option>
            <?php endforeach; ?>
        </select>
    </p>
    <fieldset>
        <legend>Emplacement</legend>
        <p>
            <label for="warehouse_aisle">Allée :</label>
            <input type="text" id="warehouse_aisle" name="warehouse_aisle" value="<?= htmlspecialchars($data['warehouse_aisle']) ?>" required>
        </p>
        <p>
            <label for="warehouse_shelf">Étagère :</label>
            <input type="text" id="warehouse_shelf" name="warehouse_shelf" value="<?= htmlspecialchars($data['warehouse_shelf']) ?>" required>
        </p>
        <p>
            <label for="warehouse_level">Niveau :</label>
            <input type="text" id="warehouse_level" name="warehouse_level" value="<?= htmlspecialchars($data['warehouse_level']) ?>" required>
        </p>
        <p>
            <label for="warehouse_position">Position :</label>
            <input type="text" id="warehouse_position" name="warehouse_position" value="<?= htmlspecialchars($data['warehouse_position']) ?>" required>
        </p>
    </fieldset>
    <p>
        <button type="submit">Modifier</button>
        <a href="/item/index">Annuler</a>
    </p>
</form>
